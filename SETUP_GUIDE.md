# AveImgCloud - Complete Setup Guide

This guide will walk you through setting up AveImgCloud from scratch to a fully functional deployment.

## 🚀 Quick Start (5 minutes)

### 1. <PERSON><PERSON> and Install
```bash
git clone https://github.com/RjNlxe/AveImgCloud.git
cd AveImgCloud
npm install
```

### 2. Environment Setup
```bash
cp .env.example .env.local
```

### 3. Appwrite Setup
1. Go to [Appwrite Console](https://cloud.appwrite.io)
2. Create a new project
3. Copy Project ID to `.env.local`

### 4. Database Initialization
```bash
# Add your Appwrite API key to .env.local first
npm run init-db
```

### 5. Start Development
```bash
npm run dev
```

Visit `http://localhost:3000` to see your application!

## 📋 Detailed Setup Instructions

### Step 1: Appwrite Project Setup

#### Create Project
1. Sign up/login to [Appwrite Console](https://cloud.appwrite.io)
2. Click "Create Project"
3. Enter project name: "AveImgCloud"
4. Copy the Project ID

#### Get API Key
1. Go to "Settings" → "API Keys"
2. Click "Create API Key"
3. Name: "Database Setup"
4. Scopes: Select all Database and Storage scopes
5. Copy the API key

#### Configure Environment
Update your `.env.local` file:
```env
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_APPWRITE_DATABASE_ID=main
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=images
APPWRITE_API_KEY=your_api_key_here
```

### Step 2: Database Setup

#### Create Database
1. In Appwrite Console, go to "Databases"
2. Click "Create Database"
3. Name: "main"
4. Copy the Database ID to `.env.local`

#### Initialize Collections
Run the database initialization script:
```bash
npm run init-db
```

This will create:
- Users collection with proper attributes and indexes
- Images collection for file metadata
- Storage plans collection with default plans
- Purchases collection for transaction history

### Step 3: Storage Setup

#### Create Storage Bucket
1. Go to "Storage" in Appwrite Console
2. Click "Create Bucket"
3. Name: "images"
4. Bucket ID: "images"
5. Copy Bucket ID to `.env.local`

#### Configure Permissions
Set these permissions for the images bucket:
- **Read**: `users` (any authenticated user)
- **Create**: `users` (any authenticated user)
- **Update**: `users` (any authenticated user)
- **Delete**: `users` (any authenticated user)

#### File Settings
- Maximum file size: 10MB (10485760 bytes)
- Allowed file extensions: jpg, jpeg, png, gif, webp
- Antivirus: Enabled (recommended)
- Encryption: Enabled (recommended)

### Step 4: Authentication Setup

#### Configure Allowed Origins
1. Go to "Auth" → "Settings"
2. Add these URLs to "Allowed Origins":
   - `http://localhost:3000` (development)
   - `https://yourdomain.com` (production)

#### Setup OAuth Providers

##### Discord OAuth
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create new application: "AveImgCloud"
3. Go to "OAuth2" section
4. Add redirect URI: `https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/discord/[YOUR_PROJECT_ID]`
5. Copy Client ID and Client Secret
6. In Appwrite Console:
   - Go to "Auth" → "Settings" → "OAuth2 Providers"
   - Enable Discord
   - Enter Client ID and Client Secret

##### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create new project or select existing
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Application type: Web application
6. Add redirect URI: `https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/google/[YOUR_PROJECT_ID]`
7. Copy Client ID and Client Secret
8. In Appwrite Console:
   - Go to "Auth" → "Settings" → "OAuth2 Providers"
   - Enable Google
   - Enter Client ID and Client Secret

### Step 5: Development

#### Start Development Server
```bash
npm run dev
```

#### Create Admin User
1. Register a new account using Discord or Google OAuth
2. In Appwrite Console, go to "Databases" → "main" → "users"
3. Find your user document
4. Edit the document and set `isAdmin` to `true`

### Step 6: Production Deployment

#### Vercel Deployment
1. Push your code to GitHub
2. Go to [Vercel Dashboard](https://vercel.com)
3. Import your GitHub repository
4. Add environment variables:
   ```
   NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
   NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_APPWRITE_DATABASE_ID=main
   NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=images
   NEXT_PUBLIC_API_BASE_URL=https://yourdomain.vercel.app
   ```
5. Deploy

#### Update Appwrite Settings
1. Add your production domain to Appwrite "Allowed Origins"
2. Update OAuth redirect URIs with production domain
3. Test authentication flows

## 🔧 Configuration Options

### Storage Plans
Default plans are created automatically:
- **Starter**: 100MB for 50 credits
- **Pro**: 500MB for 150 credits  
- **Business**: 2GB for 300 credits

You can modify these in the admin panel or directly in Appwrite Console.

### File Upload Limits
- Maximum file size: 10MB (configurable in API)
- Allowed types: JPEG, PNG, GIF, WebP
- Storage quota: Per-user limits based on purchased plans

### API Configuration
- ShareX integration with API key authentication
- RESTful API endpoints for all operations
- Comprehensive error handling and validation

## 🛠️ Troubleshooting

### Common Issues

#### "Unauthorized" errors
- Check if user is authenticated
- Verify API key is correctly set for ShareX uploads
- Ensure proper permissions on collections

#### File upload failures
- Check storage bucket permissions
- Verify file size and type restrictions
- Ensure storage quota isn't exceeded

#### OAuth not working
- Verify redirect URIs match exactly
- Check allowed origins in Appwrite settings
- Ensure OAuth apps are properly configured

#### Database errors
- Run `npm run init-db` to recreate collections
- Check API key permissions
- Verify environment variables are correct

### Debug Mode
Set `NODE_ENV=development` in your environment to enable detailed error logging.

### Getting Help
- Check the [Appwrite Documentation](https://appwrite.io/docs)
- Review [Next.js Documentation](https://nextjs.org/docs)
- Open an issue on the GitHub repository

## 🎯 Next Steps

After setup:
1. Customize the branding and colors in `tailwind.config.ts`
2. Add your own storage plans in the admin panel
3. Configure custom domains and SSL
4. Set up monitoring and analytics
5. Add custom features as needed

## 📊 Monitoring

### Key Metrics to Monitor
- User registrations and activity
- Storage usage and quota limits
- Upload success/failure rates
- API response times
- Error rates and types

### Recommended Tools
- Vercel Analytics for performance
- Appwrite Console for backend metrics
- Custom dashboard for business metrics

---

**Congratulations!** 🎉 Your AveImgCloud instance is now ready for production use.
