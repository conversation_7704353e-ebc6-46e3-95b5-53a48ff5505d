'use client';

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react';
import { Button } from './Button';
import { cn, formatFileSize, isValidFileType, isValidFileSize } from '@/lib/utils';

interface FileUploadProps {
  onUpload: (files: File[]) => void;
  maxFiles?: number;
  maxSize?: number;
  className?: string;
  disabled?: boolean;
}

interface FileWithPreview extends File {
  preview?: string;
}

export function FileUpload({ 
  onUpload, 
  maxFiles = 10, 
  maxSize = 10 * 1024 * 1024, // 10MB
  className,
  disabled = false
}: FileUploadProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    const newErrors: string[] = [];
    
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          newErrors.push(`${file.name} is too large. Maximum size is ${formatFileSize(maxSize)}.`);
        } else if (error.code === 'file-invalid-type') {
          newErrors.push(`${file.name} is not a supported image format.`);
        } else if (error.code === 'too-many-files') {
          newErrors.push(`Too many files. Maximum is ${maxFiles} files.`);
        }
      });
    });

    // Validate accepted files
    const validFiles: FileWithPreview[] = [];
    acceptedFiles.forEach((file) => {
      if (!isValidFileType(file)) {
        newErrors.push(`${file.name} is not a supported image format.`);
        return;
      }
      
      if (!isValidFileSize(file, maxSize)) {
        newErrors.push(`${file.name} is too large. Maximum size is ${formatFileSize(maxSize)}.`);
        return;
      }

      // Create preview URL
      const fileWithPreview = Object.assign(file, {
        preview: URL.createObjectURL(file)
      });
      
      validFiles.push(fileWithPreview);
    });

    setErrors(newErrors);
    
    if (validFiles.length > 0) {
      const newFiles = [...files, ...validFiles].slice(0, maxFiles);
      setFiles(newFiles);
    }
  }, [files, maxFiles, maxSize]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxFiles,
    maxSize,
    disabled
  });

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    
    // Revoke the preview URL to free memory
    if (files[index].preview) {
      URL.revokeObjectURL(files[index].preview!);
    }
  };

  const handleUpload = () => {
    if (files.length > 0) {
      onUpload(files);
      // Clear files after upload
      files.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
      setFiles([]);
      setErrors([]);
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={cn(
          'dropzone cursor-pointer',
          isDragActive && 'active',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center">
            <Upload className="w-8 h-8 text-white" />
          </div>
          
          {isDragActive ? (
            <div className="text-center">
              <p className="text-lg font-medium text-purple-400">Drop the files here...</p>
            </div>
          ) : (
            <div className="text-center">
              <p className="text-lg font-medium text-white mb-2">
                Drag & drop images here, or click to select
              </p>
              <p className="text-sm text-gray-400">
                Supports JPEG, PNG, GIF, WebP up to {formatFileSize(maxSize)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Maximum {maxFiles} files
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="space-y-2">
          {errors.map((error, index) => (
            <div key={index} className="flex items-center space-x-2 text-red-400 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}

      {/* File Preview */}
      {files.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">Selected Files ({files.length})</h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {files.map((file, index) => (
              <div key={index} className="relative bg-gray-800 rounded-lg p-4">
                <button
                  onClick={() => removeFile(index)}
                  className="absolute top-2 right-2 w-6 h-6 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-colors"
                >
                  <X className="w-4 h-4 text-white" />
                </button>
                
                <div className="space-y-3">
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-full h-32 bg-gray-700 rounded-lg flex items-center justify-center">
                      <ImageIcon className="w-8 h-8 text-gray-400" />
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium text-white truncate" title={file.name}>
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-400">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <Button 
            onClick={handleUpload}
            className="w-full"
            disabled={files.length === 0 || disabled}
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload {files.length} {files.length === 1 ? 'Image' : 'Images'}
          </Button>
        </div>
      )}
    </div>
  );
}
