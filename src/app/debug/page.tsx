'use client';

import { useAuth } from '@/contexts/AuthContext';
import { account } from '@/lib/appwrite';
import { useState } from 'react';

export default function DebugPage() {
  const { user, loading, refreshUser } = useAuth();
  const [testResult, setTestResult] = useState<any>(null);

  const testAppwriteConnection = async () => {
    try {
      console.log('Testing Appwrite connection...');
      const currentUser = await account.get();
      console.log('Current user:', currentUser);
      setTestResult({ success: true, user: currentUser });
    } catch (error: any) {
      console.error('Appwrite test error:', error);
      setTestResult({ success: false, error: error.message });
    }
  };

  const testRefreshUser = async () => {
    try {
      console.log('Testing refresh user...');
      await refreshUser();
      console.log('Refresh completed');
    } catch (error: any) {
      console.error('Refresh error:', error);
    }
  };

  const testCreateUser = async () => {
    try {
      console.log('Testing user creation...');
      const testEmail = '<EMAIL>';
      const testPassword = 'testpassword123';
      const testName = 'Test User';

      // Create account
      const newUser = await account.create('unique()', testEmail, testPassword, testName);
      console.log('User created:', newUser);

      // Login
      const session = await account.createEmailPasswordSession(testEmail, testPassword);
      console.log('Session created:', session);

      // Refresh user data
      await refreshUser();

      setTestResult({ success: true, message: 'User created and logged in successfully' });
    } catch (error: any) {
      console.error('User creation error:', error);
      setTestResult({ success: false, error: error.message });
    }
  };

  return (
    <div className="min-h-screen p-8 bg-gray-900 text-white">
      <h1 className="text-3xl font-bold mb-8">Debug Page</h1>
      
      <div className="space-y-6">
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Auth Context State</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {loading ? 'true' : 'false'}</p>
            <p><strong>User:</strong> {user ? 'Logged in' : 'Not logged in'}</p>
            {user && (
              <div className="ml-4">
                <p><strong>Name:</strong> {user.name}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>ID:</strong> {user.$id}</p>
                <p><strong>Credits:</strong> {user.credits}</p>
                <p><strong>Is Admin:</strong> {user.isAdmin ? 'Yes' : 'No'}</p>
              </div>
            )}
          </div>
        </div>

        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="space-x-4">
            <button 
              onClick={testAppwriteConnection}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
            >
              Test Appwrite Connection
            </button>
            <button
              onClick={testRefreshUser}
              className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
            >
              Test Refresh User
            </button>
            <button
              onClick={testCreateUser}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded"
            >
              Create Test User & Login
            </button>
          </div>
        </div>

        {testResult && (
          <div className="card">
            <h2 className="text-xl font-semibold mb-4">Test Result</h2>
            <pre className="bg-gray-800 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(testResult, null, 2)}
            </pre>
          </div>
        )}

        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Environment Variables</h2>
          <div className="space-y-2 text-sm">
            <p><strong>Endpoint:</strong> {process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}</p>
            <p><strong>Project ID:</strong> {process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}</p>
            <p><strong>Database ID:</strong> {process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
