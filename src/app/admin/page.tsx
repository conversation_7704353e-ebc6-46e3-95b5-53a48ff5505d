'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { useRouter } from 'next/navigation';
import { 
  Users, 
  Image as ImageIcon, 
  HardDrive, 
  DollarSign,
  TrendingUp,
  Activity,
  Calendar,
  BarChart3
} from 'lucide-react';
import { formatFileSize, formatNumber, formatCredits } from '@/lib/utils';
import { DashboardStats } from '@/lib/types';

export default function AdminOverviewPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    if (!loading && (!user || !user.isAdmin)) {
      router.push('/dashboard');
      return;
    }

    if (user?.isAdmin) {
      fetchStats();
    }
  }, [user, loading, router]);

  const fetchStats = async () => {
    try {
      setStatsLoading(true);
      const response = await fetch('/api/admin/stats');
      const result = await response.json();

      if (result.success) {
        setStats(result.data);
      } else {
        console.error('Failed to fetch admin stats:', result.error);
      }
    } catch (error) {
      console.error('Failed to fetch admin stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    );
  }

  if (!user.isAdmin) {
    return null; // Will redirect
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">Admin Overview</h1>
          <p className="text-gray-400 mt-1">
            System statistics and performance metrics
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total Users</p>
                <p className="text-2xl font-bold text-white">
                  {statsLoading ? '...' : formatNumber(stats?.totalUsers || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                <ImageIcon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total Images</p>
                <p className="text-2xl font-bold text-white">
                  {statsLoading ? '...' : formatNumber(stats?.totalImages || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                <HardDrive className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Storage Used</p>
                <p className="text-2xl font-bold text-white">
                  {statsLoading ? '...' : formatFileSize(stats?.totalStorage || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total Revenue</p>
                <p className="text-2xl font-bold text-white">
                  {statsLoading ? '...' : formatCredits(stats?.totalRevenue || 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Storage Usage Chart */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">Storage Usage</h2>
              <BarChart3 className="w-5 h-5 text-gray-400" />
            </div>
            
            {statsLoading ? (
              <div className="text-center py-8">
                <div className="spinner mx-auto"></div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">
                    {formatFileSize(stats?.storageUsage.used || 0)} of {formatFileSize(stats?.storageUsage.total || 0)} used
                  </span>
                  <span className="text-gray-400">{stats?.storageUsage.percentage || 0}%</span>
                </div>
                
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${stats?.storageUsage.percentage || 0}%` }}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-400">
                      {formatFileSize((stats?.storageUsage.total || 0) - (stats?.storageUsage.used || 0))}
                    </div>
                    <div className="text-xs text-gray-400">Available</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-400">
                      {formatFileSize(stats?.storageUsage.used || 0)}
                    </div>
                    <div className="text-xs text-gray-400">Used</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Recent Activity */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">Recent Activity</h2>
              <Activity className="w-5 h-5 text-gray-400" />
            </div>
            
            <div className="space-y-3">
              {statsLoading ? (
                <div className="text-center py-8">
                  <div className="spinner mx-auto"></div>
                </div>
              ) : (
                <>
                  <div className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm text-white">New user registered</p>
                      <p className="text-xs text-gray-400">2 minutes ago</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm text-white">Storage plan purchased</p>
                      <p className="text-xs text-gray-400">15 minutes ago</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm text-white">Bulk image upload completed</p>
                      <p className="text-xs text-gray-400">1 hour ago</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm text-white">System maintenance completed</p>
                      <p className="text-xs text-gray-400">3 hours ago</p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">Quick Actions</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => router.push('/admin/users')}
              className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors text-left"
            >
              <Users className="w-8 h-8 text-blue-400 mb-2" />
              <h3 className="font-medium text-white">Manage Users</h3>
              <p className="text-sm text-gray-400">View and edit user accounts</p>
            </button>
            
            <button
              onClick={() => router.push('/admin/store')}
              className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors text-left"
            >
              <DollarSign className="w-8 h-8 text-green-400 mb-2" />
              <h3 className="font-medium text-white">Storage Plans</h3>
              <p className="text-sm text-gray-400">Create and manage plans</p>
            </button>
            
            <button
              onClick={() => router.push('/admin/settings')}
              className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors text-left"
            >
              <Activity className="w-8 h-8 text-purple-400 mb-2" />
              <h3 className="font-medium text-white">System Settings</h3>
              <p className="text-sm text-gray-400">Configure system options</p>
            </button>
            
            <button
              onClick={() => window.open('https://cloud.appwrite.io', '_blank')}
              className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors text-left"
            >
              <BarChart3 className="w-8 h-8 text-yellow-400 mb-2" />
              <h3 className="font-medium text-white">Appwrite Console</h3>
              <p className="text-sm text-gray-400">Access backend console</p>
            </button>
          </div>
        </div>

        {/* System Health */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">System Health</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-medium text-white mb-1">API Status</h3>
              <p className="text-sm text-green-400">Operational</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <HardDrive className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-medium text-white mb-1">Storage</h3>
              <p className="text-sm text-green-400">Healthy</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Calendar className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-medium text-white mb-1">Uptime</h3>
              <p className="text-sm text-green-400">99.9%</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
