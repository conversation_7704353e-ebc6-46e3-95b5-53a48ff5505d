'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toaster';
import { useRouter } from 'next/navigation';
import { 
  Search, 
  Edit, 
  Trash2, 
  Crown, 
  User,
  Calendar,
  HardDrive,
  DollarSign
} from 'lucide-react';
import { User as UserType } from '@/lib/types';
import { formatFileSize, formatCredits, formatDate } from '@/lib/utils';

export default function AdminUsersPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { addToast } = useToast();
  const [users, setUsers] = useState<UserType[]>([]);
  const [usersLoading, setUsersLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingUser, setEditingUser] = useState<UserType | null>(null);
  const [editForm, setEditForm] = useState({
    isAdmin: false,
    credits: 0,
    storageQuota: 0
  });

  useEffect(() => {
    if (!loading && (!user || !user.isAdmin)) {
      router.push('/dashboard');
      return;
    }

    if (user?.isAdmin) {
      fetchUsers();
    }
  }, [user, loading, router, searchTerm]);

  const fetchUsers = async () => {
    try {
      setUsersLoading(true);
      const response = await fetch(`/api/admin/users?search=${encodeURIComponent(searchTerm)}`);
      const result = await response.json();
      
      if (result.success) {
        setUsers(result.data.users);
      } else {
        addToast({
          type: 'error',
          title: 'Failed to load users',
          description: result.error || 'Please try again later.',
        });
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      addToast({
        type: 'error',
        title: 'Failed to load users',
        description: 'Please try again later.',
      });
    } finally {
      setUsersLoading(false);
    }
  };

  const handleEditUser = (userToEdit: UserType) => {
    setEditingUser(userToEdit);
    setEditForm({
      isAdmin: userToEdit.isAdmin,
      credits: userToEdit.credits,
      storageQuota: userToEdit.storageQuota
    });
  };

  const handleSaveUser = async () => {
    if (!editingUser) return;

    try {
      const response = await fetch(`/api/admin/users/${editingUser.$id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      const result = await response.json();

      if (result.success) {
        addToast({
          type: 'success',
          title: 'User Updated',
          description: 'User has been updated successfully.',
        });
        setEditingUser(null);
        await fetchUsers();
      } else {
        addToast({
          type: 'error',
          title: 'Update Failed',
          description: result.error || 'Failed to update user.',
        });
      }
    } catch (error) {
      console.error('Update failed:', error);
      addToast({
        type: 'error',
        title: 'Update Failed',
        description: 'Failed to update user.',
      });
    }
  };

  const handleDeleteUser = async (userId: string, userName: string) => {
    if (!confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        addToast({
          type: 'success',
          title: 'User Deleted',
          description: 'User has been deleted successfully.',
        });
        await fetchUsers();
      } else {
        addToast({
          type: 'error',
          title: 'Delete Failed',
          description: result.error || 'Failed to delete user.',
        });
      }
    } catch (error) {
      console.error('Delete failed:', error);
      addToast({
        type: 'error',
        title: 'Delete Failed',
        description: 'Failed to delete user.',
      });
    }
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    );
  }

  if (!user.isAdmin) {
    return null; // Will redirect
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">User Management</h1>
            <p className="text-gray-400 mt-1">
              Manage user accounts, permissions, and storage quotas
            </p>
          </div>
        </div>

        {/* Search */}
        <div className="card">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button onClick={fetchUsers}>
              Search
            </Button>
          </div>
        </div>

        {/* Users Table */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">
            Users ({users.length})
          </h2>

          {usersLoading ? (
            <div className="text-center py-12">
              <div className="spinner mx-auto mb-4"></div>
              <p className="text-gray-400">Loading users...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <User className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 text-lg mb-2">
                {searchTerm ? 'No users found' : 'No users yet'}
              </p>
              <p className="text-gray-500">
                {searchTerm ? 'Try adjusting your search terms' : 'Users will appear here once they register'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">User</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Role</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Credits</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Storage</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Joined</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((userItem) => (
                    <tr key={userItem.$id} className="border-b border-gray-800 hover:bg-gray-800/50">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {userItem.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="text-white font-medium">{userItem.name}</p>
                            <p className="text-gray-400 text-sm">{userItem.email}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {userItem.isAdmin ? (
                          <div className="flex items-center space-x-2 text-yellow-400">
                            <Crown className="w-4 h-4" />
                            <span>Admin</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">User</span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-white">{formatCredits(userItem.credits)}</span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          <div className="text-white">
                            {formatFileSize(userItem.storageUsed)} / {formatFileSize(userItem.storageQuota)}
                          </div>
                          <div className="text-gray-400">
                            {Math.round((userItem.storageUsed / userItem.storageQuota) * 100)}% used
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-gray-400">{formatDate(userItem.$createdAt)}</span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditUser(userItem)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          {userItem.$id !== user.$id && (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteUser(userItem.$id, userItem.name)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Edit User Modal */}
        {editingUser && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
              <h3 className="text-xl font-semibold text-white mb-4">
                Edit User: {editingUser.name}
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isAdmin"
                    checked={editForm.isAdmin}
                    onChange={(e) => setEditForm({ ...editForm, isAdmin: e.target.checked })}
                    className="rounded"
                  />
                  <label htmlFor="isAdmin" className="text-white">
                    Administrator privileges
                  </label>
                </div>
                
                <Input
                  label="Credits"
                  type="number"
                  value={editForm.credits}
                  onChange={(e) => setEditForm({ ...editForm, credits: parseFloat(e.target.value) || 0 })}
                  min="0"
                  step="0.01"
                />
                
                <Input
                  label="Storage Quota (MB)"
                  type="number"
                  value={Math.round(editForm.storageQuota / 1024 / 1024)}
                  onChange={(e) => setEditForm({ 
                    ...editForm, 
                    storageQuota: (parseFloat(e.target.value) || 0) * 1024 * 1024 
                  })}
                  min="0"
                />
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <Button variant="ghost" onClick={() => setEditingUser(null)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveUser}>
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
