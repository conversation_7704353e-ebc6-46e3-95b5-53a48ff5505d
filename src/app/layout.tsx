import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { Toaster } from '@/components/ui/Toaster';
import { StarField } from '@/components/ui/StarField';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'AveImgCloud - Premium Image Hosting',
  description: 'Professional image hosting service with ShareX integration, custom embeds, and powerful management tools.',
  keywords: ['image hosting', 'ShareX', 'file upload', 'cloud storage'],
  authors: [{ name: 'AveImgCloud Team' }],
  openGraph: {
    title: 'AveImgCloud - Premium Image Hosting',
    description: 'Professional image hosting service with ShareX integration, custom embeds, and powerful management tools.',
    type: 'website',
    url: 'https://aveimgcloud.vercel.app',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AveImgCloud - Premium Image Hosting',
    description: 'Professional image hosting service with ShareX integration, custom embeds, and powerful management tools.',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <StarField />
        <AuthProvider>
          {children}
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
}
