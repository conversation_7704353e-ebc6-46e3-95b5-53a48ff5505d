import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser, Query } from '@/lib/appwrite';
import { DashboardStats } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin status
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    if (!userDoc.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get total users
    const users = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.USERS,
      [Query.limit(1)]
    );

    // Get total images
    const images = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      [Query.limit(1)]
    );

    // Get recent uploads
    const recentUploads = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      [
        Query.orderDesc('$createdAt'),
        Query.limit(10)
      ]
    );

    // Get total revenue from purchases
    const purchases = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.PURCHASES,
      [
        Query.equal('status', 'completed'),
        Query.limit(1000) // Adjust based on your needs
      ]
    );

    const totalRevenue = purchases.documents.reduce((total, purchase) => {
      return total + (purchase.price || 0);
    }, 0);

    // Calculate total storage used
    const allUsers = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.USERS,
      [Query.limit(1000)] // Adjust based on your needs
    );

    const totalStorageUsed = allUsers.documents.reduce((total, user) => {
      return total + (user.storageUsed || 0);
    }, 0);

    const totalStorageQuota = allUsers.documents.reduce((total, user) => {
      return total + (user.storageQuota || 0);
    }, 0);

    const stats: DashboardStats = {
      totalUsers: users.total,
      totalImages: images.total,
      totalStorage: totalStorageUsed,
      totalRevenue,
      recentUploads: recentUploads.documents as any[],
      storageUsage: {
        used: totalStorageUsed,
        total: totalStorageQuota,
        percentage: totalStorageQuota > 0 ? Math.round((totalStorageUsed / totalStorageQuota) * 100) : 0
      }
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('Admin stats error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch admin stats' },
      { status: 500 }
    );
  }
}
