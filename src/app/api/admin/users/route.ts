import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser, Query } from '@/lib/appwrite';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin status
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    if (!userDoc.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';

    const offset = (page - 1) * limit;

    let queries = [
      Query.orderDesc('$createdAt'),
      Query.limit(limit),
      Query.offset(offset)
    ];

    // Add search filter if provided
    if (search) {
      queries.push(Query.search('name', search));
    }

    // Get all users
    const users = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.USERS,
      queries
    );

    return NextResponse.json({
      success: true,
      data: {
        users: users.documents,
        total: users.total,
        page,
        limit,
        totalPages: Math.ceil(users.total / limit)
      }
    });

  } catch (error: any) {
    console.error('Admin users fetch error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch users' },
      { status: 500 }
    );
  }
}
