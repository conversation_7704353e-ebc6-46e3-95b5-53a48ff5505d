import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser } from '@/lib/appwrite';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin status
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    if (!userDoc.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const userId = params.id;
    const body = await request.json();
    const { isAdmin, credits, storageQuota } = body;

    // Validate input
    const updateData: any = {};
    
    if (typeof isAdmin === 'boolean') {
      updateData.isAdmin = isAdmin;
    }
    
    if (typeof credits === 'number' && credits >= 0) {
      updateData.credits = credits;
    }
    
    if (typeof storageQuota === 'number' && storageQuota >= 0) {
      updateData.storageQuota = storageQuota;
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Update user
    const updatedUser = await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USERS,
      userId,
      updateData
    );

    return NextResponse.json({
      success: true,
      data: updatedUser
    });

  } catch (error: any) {
    console.error('Admin user update error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin status
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    if (!userDoc.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const userId = params.id;

    // Prevent admin from deleting themselves
    if (userId === user.$id) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Delete user's images first
    const userImages = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      [Query.equal('userId', userId)]
    );

    // Delete all user images
    for (const image of userImages.documents) {
      await databases.deleteDocument(DATABASE_ID, COLLECTIONS.IMAGES, image.$id);
    }

    // Delete user purchases
    const userPurchases = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.PURCHASES,
      [Query.equal('userId', userId)]
    );

    for (const purchase of userPurchases.documents) {
      await databases.deleteDocument(DATABASE_ID, COLLECTIONS.PURCHASES, purchase.$id);
    }

    // Delete user
    await databases.deleteDocument(DATABASE_ID, COLLECTIONS.USERS, userId);

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error: any) {
    console.error('Admin user deletion error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete user' },
      { status: 500 }
    );
  }
}
