import { NextRequest, NextResponse } from 'next/server';
import { storage, databases, DATABASE_ID, COLLECTIONS, getCurrentUser } from '@/lib/appwrite';
import { generateUniqueFilename, isValidFileType, isValidFileSize } from '@/lib/utils';
import { authenticateApi<PERSON>ey } from '@/lib/auth-middleware';
import { ID } from 'appwrite';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const STORAGE_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID!;

export async function POST(request: NextRequest) {
  try {
    // Check authentication (either session or API key)
    let user = await getCurrentUser();
    let userDoc = null;

    if (!user) {
      // Try API key authentication for ShareX
      userDoc = await authenticateApiKey(request);
      if (!userDoc) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      user = { $id: userDoc.$id }; // Create minimal user object
    } else {
      // Get user data from database for session auth
      userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    }

    // userDoc is already available from authentication above
    
    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!isValidFileType(file)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only images are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size
    if (!isValidFileSize(file, MAX_FILE_SIZE)) {
      return NextResponse.json(
        { success: false, error: `File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB.` },
        { status: 400 }
      );
    }

    // Check storage quota
    const newStorageUsed = userDoc.storageUsed + file.size;
    if (newStorageUsed > userDoc.storageQuota) {
      return NextResponse.json(
        { success: false, error: 'Storage quota exceeded. Please upgrade your plan.' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileName = generateUniqueFilename(file.name);

    // Upload file to Appwrite Storage
    const uploadedFile = await storage.createFile(
      STORAGE_BUCKET_ID,
      ID.unique(),
      file
    );

    // Get file URL
    const fileUrl = storage.getFileView(STORAGE_BUCKET_ID, uploadedFile.$id);

    // Create image record in database
    const imageDoc = await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      ID.unique(),
      {
        userId: user.$id,
        fileName: fileName,
        originalName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        url: fileUrl.toString(),
        fileId: uploadedFile.$id, // Store the Appwrite file ID for deletion
        isPublic: true,
        downloadCount: 0,
        tags: [],
      }
    );

    // Update user storage usage
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USERS,
      user.$id,
      {
        storageUsed: newStorageUsed,
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        id: imageDoc.$id,
        url: fileUrl.toString(),
        fileName: fileName,
        originalName: file.name,
        fileSize: file.size,
        mimeType: file.type,
      },
    });

  } catch (error: any) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Upload failed' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
