import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser, Query } from '@/lib/appwrite';
import { UserStats } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user data from database
    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    
    // Get user's images
    const images = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      [
        Query.equal('userId', user.$id),
        Query.orderDesc('$createdAt'),
        Query.limit(10)
      ]
    );

    // Calculate total download count
    const totalDownloadCount = images.documents.reduce((total, image) => {
      return total + (image.downloadCount || 0);
    }, 0);

    const stats: UserStats = {
      totalImages: images.total,
      storageUsed: userDoc.storageUsed || 0,
      storageQuota: userDoc.storageQuota || 0,
      downloadCount: totalDownloadCount,
      recentImages: images.documents.slice(0, 5) as any[]
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('Stats error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch stats' },
      { status: 500 }
    );
  }
}
