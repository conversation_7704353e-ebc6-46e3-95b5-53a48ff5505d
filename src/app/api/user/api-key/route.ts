import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser } from '@/lib/appwrite';
import { generateApiKey } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Generate new API key
    const newApiKey = generateApiKey();

    // Update user with new API key
    const updatedUser = await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USERS,
      user.$id,
      {
        apiKey: newApiKey
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        apiKey: newApiKey
      }
    });

  } catch (error: any) {
    console.error('API key generation error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to generate API key' },
      { status: 500 }
    );
  }
}
