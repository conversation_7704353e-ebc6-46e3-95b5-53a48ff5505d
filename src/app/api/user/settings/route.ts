import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser } from '@/lib/appwrite';

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, embedTitle, embedDescription, embedFooter } = body;

    // Validate input
    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      );
    }

    // Update user settings
    const updatedUser = await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USERS,
      user.$id,
      {
        name: name.trim(),
        embedTitle: embedTitle || 'AveImgCloud',
        embedDescription: embedDescription || 'Image hosted on AveImgCloud',
        embedFooter: embedFooter || 'Powered by AveImgCloud'
      }
    );

    return NextResponse.json({
      success: true,
      data: updatedUser
    });

  } catch (error: any) {
    console.error('Settings update error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update settings' },
      { status: 500 }
    );
  }
}
