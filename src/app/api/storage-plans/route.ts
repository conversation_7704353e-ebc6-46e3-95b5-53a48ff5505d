import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser, Query } from '@/lib/appwrite';

export async function GET(request: NextRequest) {
  try {
    // Get active storage plans
    const plans = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.STORAGE_PLANS,
      [
        Query.equal('isActive', true),
        Query.orderAsc('price')
      ]
    );

    return NextResponse.json({
      success: true,
      data: plans.documents
    });

  } catch (error: any) {
    console.error('Storage plans fetch error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch storage plans' },
      { status: 500 }
    );
  }
}
