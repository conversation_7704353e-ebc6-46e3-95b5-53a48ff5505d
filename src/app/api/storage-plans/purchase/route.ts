import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser } from '@/lib/appwrite';
import { ID } from 'appwrite';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { planId } = body;

    if (!planId) {
      return NextResponse.json(
        { success: false, error: 'Plan ID is required' },
        { status: 400 }
      );
    }

    // Get user data
    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    
    // Get plan data
    const planDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.STORAGE_PLANS, planId);

    if (!planDoc.isActive) {
      return NextResponse.json(
        { success: false, error: 'Plan is not available' },
        { status: 400 }
      );
    }

    // Check if user has enough credits
    if (userDoc.credits < planDoc.price) {
      return NextResponse.json(
        { success: false, error: 'Insufficient credits' },
        { status: 400 }
      );
    }

    // Create purchase record
    const purchase = await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.PURCHASES,
      ID.unique(),
      {
        userId: user.$id,
        planId: planDoc.$id,
        planName: planDoc.name,
        price: planDoc.price,
        storageAmount: planDoc.storageAmount,
        status: 'completed'
      }
    );

    // Update user credits and storage quota
    const newCredits = userDoc.credits - planDoc.price;
    const newStorageQuota = userDoc.storageQuota + planDoc.storageAmount;

    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USERS,
      user.$id,
      {
        credits: newCredits,
        storageQuota: newStorageQuota
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        purchase,
        newCredits,
        newStorageQuota
      }
    });

  } catch (error: any) {
    console.error('Purchase error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Purchase failed' },
      { status: 500 }
    );
  }
}
