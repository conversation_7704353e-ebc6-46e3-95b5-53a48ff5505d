import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/appwrite';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing authentication...');
    const user = await getCurrentUser();
    console.log('👤 Current user:', user);
    
    return NextResponse.json({
      success: true,
      user: user,
      authenticated: !!user
    });
  } catch (error: any) {
    console.error('❌ Auth test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      authenticated: false
    });
  }
}
