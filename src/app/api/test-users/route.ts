import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS } from '@/lib/appwrite';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing user collection...');
    
    // Try to list users from the database
    const users = await databases.listDocuments(DATABASE_ID, COLLECTIONS.USERS);
    console.log('👥 Users in database:', users);
    
    return NextResponse.json({
      success: true,
      userCount: users.total,
      users: users.documents.map(user => ({
        id: user.$id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin
      }))
    });
  } catch (error: any) {
    console.error('❌ User test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    });
  }
}
