import { NextRequest, NextResponse } from 'next/server';
import { databases, storage, DATABASE_ID, COLLECTIONS, STORAGE_BUCKET_ID, getCurrentUser } from '@/lib/appwrite';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const imageId = params.id;

    // Get image document
    const imageDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.IMAGES, imageId);

    // Check if user owns the image or is admin
    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, user.$id);
    if (imageDoc.userId !== user.$id && !userDoc.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Delete file from storage
    try {
      // Extract file ID from URL or use a stored fileId
      const fileId = imageDoc.fileId || imageId; // Assuming we store fileId in the document
      await storage.deleteFile(STORAGE_BUCKET_ID, fileId);
    } catch (storageError) {
      console.warn('Failed to delete file from storage:', storageError);
      // Continue with database deletion even if storage deletion fails
    }

    // Delete image document
    await databases.deleteDocument(DATABASE_ID, COLLECTIONS.IMAGES, imageId);

    // Update user storage usage
    const ownerDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, imageDoc.userId);
    const newStorageUsed = Math.max(0, ownerDoc.storageUsed - imageDoc.fileSize);
    
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USERS,
      imageDoc.userId,
      {
        storageUsed: newStorageUsed
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error: any) {
    console.error('Image deletion error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete image' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const imageId = params.id;

    // Get image document
    const imageDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.IMAGES, imageId);

    // Increment download count
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      imageId,
      {
        downloadCount: (imageDoc.downloadCount || 0) + 1
      }
    );

    return NextResponse.json({
      success: true,
      data: imageDoc
    });

  } catch (error: any) {
    console.error('Image fetch error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Image not found' },
      { status: 404 }
    );
  }
}
