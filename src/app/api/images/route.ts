import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, getCurrentUser, Query } from '@/lib/appwrite';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';

    const offset = (page - 1) * limit;

    let queries = [
      Query.equal('userId', user.$id),
      Query.orderDesc('$createdAt'),
      Query.limit(limit),
      Query.offset(offset)
    ];

    // Add search filter if provided
    if (search) {
      queries.push(Query.search('originalName', search));
    }

    // Get user's images
    const images = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.IMAGES,
      queries
    );

    return NextResponse.json({
      success: true,
      data: {
        images: images.documents,
        total: images.total,
        page,
        limit,
        totalPages: Math.ceil(images.total / limit)
      }
    });

  } catch (error: any) {
    console.error('Images fetch error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch images' },
      { status: 500 }
    );
  }
}
