'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { 
  Upload, 
  Image as ImageIcon, 
  HardDrive, 
  Download,
  TrendingUp,
  Calendar,
  Eye
} from 'lucide-react';
import { formatFileSize, formatNumber, calculateStoragePercentage } from '@/lib/utils';
import { UserStats } from '@/lib/types';
import Link from 'next/link';

export default function DashboardPage() {
  const { user, loading: authLoading } = useAuth();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Debug logging
  console.log('🏠 Dashboard - Auth loading:', authLoading, 'User:', user);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/user/stats');
        const result = await response.json();

        if (result.success) {
          setStats(result.data);
        } else {
          console.error('Failed to fetch stats:', result.error);
        }
      } catch (error) {
        console.error('Failed to fetch stats:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchStats();
    }
  }, [user]);

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-gray-300">Loading authentication...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 mb-4">❌ No user found</div>
          <p className="text-gray-300">Please log in again</p>
          <a href="/auth/login" className="text-purple-400 hover:text-purple-300 mt-2 inline-block">
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  const storagePercentage = stats ? calculateStoragePercentage(stats.storageUsed, stats.storageQuota) : 0;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              Welcome back, {user.name}!
            </h1>
            <p className="text-gray-400 mt-1">
              Here's what's happening with your images today.
            </p>
          </div>
          <Link href="/dashboard/images">
            <Button className="mt-4 sm:mt-0">
              <Upload className="w-4 h-4 mr-2" />
              Upload Images
            </Button>
          </Link>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                <ImageIcon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total Images</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : formatNumber(stats?.totalImages || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <Download className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total Downloads</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : formatNumber(stats?.downloadCount || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                <HardDrive className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Storage Used</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : formatFileSize(stats?.storageUsed || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Credits</p>
                <p className="text-2xl font-bold text-white">
                  {user.credits?.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Storage Usage */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">Storage Usage</h2>
            <Link href="/dashboard/store">
              <Button variant="outline" size="sm">
                Upgrade Storage
              </Button>
            </Link>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">
                {formatFileSize(stats?.storageUsed || 0)} of {formatFileSize(stats?.storageQuota || 0)} used
              </span>
              <span className="text-gray-400">{storagePercentage}%</span>
            </div>
            
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${storagePercentage}%` }}
              />
            </div>
            
            {storagePercentage > 80 && (
              <p className="text-sm text-yellow-400">
                ⚠️ You're running low on storage space. Consider upgrading your plan.
              </p>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Link href="/dashboard/images" className="card hover-glow cursor-pointer">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Upload className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Upload Images</h3>
              <p className="text-gray-400">
                Drag and drop or browse to upload your images
              </p>
            </div>
          </Link>

          <Link href="/dashboard/connections" className="card hover-glow cursor-pointer">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Eye className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">ShareX Setup</h3>
              <p className="text-gray-400">
                Configure ShareX for instant uploads
              </p>
            </div>
          </Link>

          <Link href="/dashboard/settings" className="card hover-glow cursor-pointer">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Customize Embeds</h3>
              <p className="text-gray-400">
                Personalize how your images appear when shared
              </p>
            </div>
          </Link>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">Recent Activity</h2>
          <div className="space-y-3">
            {loading ? (
              <div className="text-center py-8">
                <div className="spinner mx-auto"></div>
                <p className="text-gray-400 mt-2">Loading recent activity...</p>
              </div>
            ) : stats?.recentImages.length === 0 ? (
              <div className="text-center py-8">
                <ImageIcon className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400">No recent uploads</p>
                <p className="text-sm text-gray-500 mt-1">
                  Upload your first image to get started!
                </p>
              </div>
            ) : (
              stats?.recentImages.map((image) => (
                <div key={image.$id} className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                    <ImageIcon className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-white">{image.originalName}</p>
                    <p className="text-xs text-gray-400">{formatFileSize(image.fileSize)}</p>
                  </div>
                  <div className="text-xs text-gray-400">
                    {new Date(image.$createdAt).toLocaleDateString()}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
