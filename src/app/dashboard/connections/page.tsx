'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toaster';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Download, 
  Copy, 
  RefreshCw, 
  Key, 
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { generateApiKey, copyToClipboard } from '@/lib/utils';
import { ShareXConfig } from '@/lib/types';

export default function ConnectionsPage() {
  const { user, refreshUser } = useAuth();
  const { addToast } = useToast();
  const [apiKey, setApiKey] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user?.apiKey) {
      setApiKey(user.apiKey);
    }
  }, [user]);

  const generateNewApiKey = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/api-key', {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        setApiKey(result.data.apiKey);
        addToast({
          type: 'success',
          title: 'API Key Generated',
          description: 'New API key has been generated successfully.',
        });
        await refreshUser();
      } else {
        addToast({
          type: 'error',
          title: 'Failed to generate API key',
          description: result.error || 'Please try again.',
        });
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Failed to generate API key',
        description: 'Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const copyApiKey = async () => {
    const success = await copyToClipboard(apiKey);
    if (success) {
      addToast({
        type: 'success',
        title: 'API Key Copied',
        description: 'API key copied to clipboard.',
      });
    }
  };

  const generateShareXConfig = (): ShareXConfig => {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || window.location.origin;
    
    return {
      Version: "15.0.0",
      Name: "AveImgCloud",
      DestinationType: "ImageUploader",
      RequestMethod: "POST",
      RequestURL: `${baseUrl}/api/upload`,
      Headers: {
        "Authorization": `Bearer ${apiKey}`
      },
      Body: "MultipartFormData",
      URL: "$json:data.url$",
      ThumbnailURL: "$json:data.url$",
      DeletionURL: `${baseUrl}/api/delete/$json:data.id$`
    };
  };

  const downloadShareXConfig = () => {
    if (!apiKey) {
      addToast({
        type: 'error',
        title: 'No API Key',
        description: 'Please generate an API key first.',
      });
      return;
    }

    const config = generateShareXConfig();
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'AveImgCloud.sxcu';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    addToast({
      type: 'success',
      title: 'Config Downloaded',
      description: 'ShareX configuration file downloaded successfully.',
    });
  };

  const copyShareXConfig = async () => {
    if (!apiKey) {
      addToast({
        type: 'error',
        title: 'No API Key',
        description: 'Please generate an API key first.',
      });
      return;
    }

    const config = generateShareXConfig();
    const success = await copyToClipboard(JSON.stringify(config, null, 2));
    
    if (success) {
      addToast({
        type: 'success',
        title: 'Config Copied',
        description: 'ShareX configuration copied to clipboard.',
      });
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">Connections</h1>
          <p className="text-gray-400 mt-1">
            Set up ShareX integration for instant image uploads
          </p>
        </div>

        {/* API Key Section */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">API Key</h2>
            <Button
              onClick={generateNewApiKey}
              loading={loading}
              size="sm"
              variant="outline"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Generate New Key
            </Button>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2 p-3 bg-yellow-600/20 text-yellow-400 rounded-lg text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>Keep your API key secure. Don't share it publicly.</span>
            </div>

            <div className="flex space-x-2">
              <Input
                value={apiKey || 'No API key generated'}
                readOnly
                className="font-mono"
              />
              <Button onClick={copyApiKey} disabled={!apiKey}>
                <Copy className="w-4 h-4" />
              </Button>
            </div>

            {!apiKey && (
              <p className="text-sm text-gray-400">
                Generate an API key to enable ShareX integration and API access.
              </p>
            )}
          </div>
        </div>

        {/* ShareX Configuration */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">ShareX Configuration</h2>
          
          <div className="space-y-6">
            {/* Download Config */}
            <div>
              <h3 className="text-lg font-medium text-white mb-3">Quick Setup</h3>
              <div className="flex space-x-3">
                <Button onClick={downloadShareXConfig} disabled={!apiKey}>
                  <Download className="w-4 h-4 mr-2" />
                  Download Config File
                </Button>
                <Button onClick={copyShareXConfig} disabled={!apiKey} variant="outline">
                  <Copy className="w-4 h-4 mr-2" />
                  Copy Config
                </Button>
              </div>
              <p className="text-sm text-gray-400 mt-2">
                Download the .sxcu file and import it into ShareX for instant setup.
              </p>
            </div>

            {/* Manual Setup Instructions */}
            <div>
              <h3 className="text-lg font-medium text-white mb-3">Manual Setup Instructions</h3>
              <div className="space-y-4">
                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      1
                    </div>
                    <div>
                      <h4 className="font-medium text-white">Open ShareX</h4>
                      <p className="text-sm text-gray-400 mt-1">
                        Launch ShareX and go to Destinations → Custom uploader settings
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      2
                    </div>
                    <div>
                      <h4 className="font-medium text-white">Create New Uploader</h4>
                      <p className="text-sm text-gray-400 mt-1">
                        Click "New" and name it "AveImgCloud"
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      3
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-white">Configure Settings</h4>
                      <div className="mt-2 space-y-2 text-sm">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-gray-400">Request URL:</label>
                            <code className="block bg-gray-900 p-2 rounded text-green-400 text-xs mt-1">
                              {process.env.NEXT_PUBLIC_API_BASE_URL || window.location.origin}/api/upload
                            </code>
                          </div>
                          <div>
                            <label className="text-gray-400">Request Method:</label>
                            <code className="block bg-gray-900 p-2 rounded text-green-400 text-xs mt-1">
                              POST
                            </code>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      4
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-white">Add Authorization Header</h4>
                      <div className="mt-2 space-y-2">
                        <div>
                          <label className="text-gray-400 text-sm">Header Name:</label>
                          <code className="block bg-gray-900 p-2 rounded text-green-400 text-xs mt-1">
                            Authorization
                          </code>
                        </div>
                        <div>
                          <label className="text-gray-400 text-sm">Header Value:</label>
                          <code className="block bg-gray-900 p-2 rounded text-green-400 text-xs mt-1">
                            Bearer {apiKey || 'YOUR_API_KEY'}
                          </code>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      5
                    </div>
                    <div>
                      <h4 className="font-medium text-white">Set Response URLs</h4>
                      <div className="mt-2 space-y-2 text-sm">
                        <div>
                          <label className="text-gray-400">URL:</label>
                          <code className="block bg-gray-900 p-2 rounded text-green-400 text-xs mt-1">
                            $json:data.url$
                          </code>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-sm">
                      <CheckCircle className="w-4 h-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-white">Test & Use</h4>
                      <p className="text-sm text-gray-400 mt-1">
                        Test the uploader and set it as your default image uploader
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Resources */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-medium text-white mb-3">Additional Resources</h3>
              <div className="space-y-3">
                <a
                  href="https://getsharex.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>Download ShareX</span>
                </a>
                <a
                  href="https://getsharex.com/docs/custom-uploader"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>ShareX Custom Uploader Documentation</span>
                </a>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-blue-600/20 border border-blue-600/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-blue-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-400 mb-2">Pro Tips</h4>
                  <ul className="text-sm text-blue-300 space-y-1">
                    <li>• Use keyboard shortcuts in ShareX for faster uploads</li>
                    <li>• Set up automatic clipboard copying for instant sharing</li>
                    <li>• Configure different upload destinations for different file types</li>
                    <li>• Enable upload history to keep track of your uploads</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
