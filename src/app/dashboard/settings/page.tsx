'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toaster';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Save, 
  Eye, 
  User, 
  MessageSquare,
  Palette,
  Monitor
} from 'lucide-react';

export default function SettingsPage() {
  const { user, refreshUser } = useAuth();
  const { addToast } = useToast();
  const [loading, setLoading] = useState(false);
  
  // Profile settings
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  
  // Embed settings
  const [embedTitle, setEmbedTitle] = useState('');
  const [embedDescription, setEmbedDescription] = useState('');
  const [embedFooter, setEmbedFooter] = useState('');

  useEffect(() => {
    if (user) {
      setName(user.name || '');
      setEmail(user.email || '');
      setEmbedTitle(user.embedTitle || 'AveImgCloud');
      setEmbedDescription(user.embedDescription || 'Image hosted on AveImgCloud');
      setEmbedFooter(user.embedFooter || 'Powered by AveImgCloud');
    }
  }, [user]);

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name }),
      });

      const result = await response.json();

      if (result.success) {
        addToast({
          type: 'success',
          title: 'Profile Updated',
          description: 'Your profile has been updated successfully.',
        });
        await refreshUser();
      } else {
        addToast({
          type: 'error',
          title: 'Update Failed',
          description: result.error || 'Failed to update profile. Please try again.',
        });
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Update Failed',
        description: 'Failed to update profile. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveEmbeds = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          embedTitle,
          embedDescription,
          embedFooter
        }),
      });

      const result = await response.json();

      if (result.success) {
        addToast({
          type: 'success',
          title: 'Embed Settings Updated',
          description: 'Your embed settings have been updated successfully.',
        });
        await refreshUser();
      } else {
        addToast({
          type: 'error',
          title: 'Update Failed',
          description: result.error || 'Failed to update embed settings. Please try again.',
        });
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Update Failed',
        description: 'Failed to update embed settings. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const resetEmbedDefaults = () => {
    setEmbedTitle('AveImgCloud');
    setEmbedDescription('Image hosted on AveImgCloud');
    setEmbedFooter('Powered by AveImgCloud');
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">Settings</h1>
          <p className="text-gray-400 mt-1">
            Manage your profile and customize your image embeds
          </p>
        </div>

        {/* Profile Settings */}
        <div className="card">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
            <h2 className="text-xl font-semibold text-white">Profile Settings</h2>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Full Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your full name"
              />
              <Input
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                disabled // Email changes might require re-verification
              />
            </div>

            <div className="flex justify-end">
              <Button onClick={handleSaveProfile} loading={loading}>
                <Save className="w-4 h-4 mr-2" />
                Save Profile
              </Button>
            </div>
          </div>
        </div>

        {/* Embed Customization */}
        <div className="card">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-white" />
            </div>
            <h2 className="text-xl font-semibold text-white">Embed Customization</h2>
          </div>

          <div className="space-y-6">
            <div className="space-y-4">
              <Input
                label="Embed Title"
                value={embedTitle}
                onChange={(e) => setEmbedTitle(e.target.value)}
                placeholder="Title shown in embeds"
                maxLength={256}
              />
              
              <div>
                <label className="text-sm font-medium text-gray-300 block mb-2">
                  Embed Description
                </label>
                <textarea
                  value={embedDescription}
                  onChange={(e) => setEmbedDescription(e.target.value)}
                  placeholder="Description shown in embeds"
                  maxLength={4096}
                  rows={3}
                  className="w-full rounded-lg border border-gray-700 bg-gray-800 px-3 py-2 text-sm text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                />
              </div>

              <Input
                label="Embed Footer"
                value={embedFooter}
                onChange={(e) => setEmbedFooter(e.target.value)}
                placeholder="Footer text shown in embeds"
                maxLength={2048}
              />
            </div>

            <div className="flex items-center justify-between">
              <Button variant="outline" onClick={resetEmbedDefaults}>
                Reset to Defaults
              </Button>
              <Button onClick={handleSaveEmbeds} loading={loading}>
                <Save className="w-4 h-4 mr-2" />
                Save Embed Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Embed Preview */}
        <div className="card">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
              <Eye className="w-5 h-5 text-white" />
            </div>
            <h2 className="text-xl font-semibold text-white">Embed Preview</h2>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-gray-400">
              This is how your images will appear when shared on Discord and other platforms:
            </p>

            {/* Discord-style embed preview */}
            <div className="bg-gray-800 border-l-4 border-purple-500 p-4 rounded-r-lg max-w-md">
              <div className="space-y-2">
                {embedTitle && (
                  <h3 className="text-blue-400 font-medium text-sm hover:underline cursor-pointer">
                    {embedTitle}
                  </h3>
                )}
                
                {embedDescription && (
                  <p className="text-gray-300 text-sm">
                    {embedDescription}
                  </p>
                )}
                
                <div className="bg-gray-700 rounded-lg p-2">
                  <div className="w-full h-32 bg-gray-600 rounded flex items-center justify-center">
                    <Monitor className="w-8 h-8 text-gray-400" />
                  </div>
                </div>
                
                {embedFooter && (
                  <p className="text-gray-500 text-xs">
                    {embedFooter}
                  </p>
                )}
              </div>
            </div>

            <div className="bg-blue-600/20 border border-blue-600/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Palette className="w-5 h-5 text-blue-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-400 mb-2">Embed Tips</h4>
                  <ul className="text-sm text-blue-300 space-y-1">
                    <li>• Keep titles short and descriptive (under 256 characters)</li>
                    <li>• Descriptions can include basic formatting and links</li>
                    <li>• Footer text appears at the bottom in smaller text</li>
                    <li>• Changes apply to all future uploads</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Account Information */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">Account Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-400">Account Type</label>
              <p className="text-white mt-1">
                {user?.isAdmin ? 'Administrator' : 'Regular User'}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Credits</label>
              <p className="text-white mt-1">
                {user?.credits?.toFixed(2) || '0.00'}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Storage Quota</label>
              <p className="text-white mt-1">
                {user?.storageQuota ? `${(user.storageQuota / 1024 / 1024).toFixed(0)} MB` : 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Member Since</label>
              <p className="text-white mt-1">
                {user?.$createdAt ? new Date(user.$createdAt).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
