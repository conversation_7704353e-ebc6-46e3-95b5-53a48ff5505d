'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { FileUpload } from '@/components/ui/FileUpload';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toaster';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Search, 
  Grid, 
  List, 
  Copy, 
  ExternalLink, 
  Trash2,
  Eye,
  Download,
  Calendar,
  FileImage
} from 'lucide-react';
import { Image } from '@/lib/types';
import { formatFileSize, formatDate, copyToClipboard } from '@/lib/utils';

export default function ImagesPage() {
  const { user } = useAuth();
  const { addToast } = useToast();
  const [images, setImages] = useState<Image[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/images?search=${encodeURIComponent(searchTerm)}`);
      const result = await response.json();

      if (result.success) {
        setImages(result.data.images);
      } else {
        addToast({
          type: 'error',
          title: 'Failed to load images',
          description: result.error || 'Please try again later.',
        });
      }
    } catch (error) {
      console.error('Failed to fetch images:', error);
      addToast({
        type: 'error',
        title: 'Failed to load images',
        description: 'Please try again later.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (files: File[]) => {
    setUploading(true);

    try {
      const uploadPromises = files.map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error || 'Upload failed');
        }

        return result.data;
      });

      await Promise.all(uploadPromises);

      addToast({
        type: 'success',
        title: 'Upload successful',
        description: `${files.length} ${files.length === 1 ? 'image' : 'images'} uploaded successfully.`,
      });

      // Refresh images list
      await fetchImages();
    } catch (error: any) {
      console.error('Upload failed:', error);
      addToast({
        type: 'error',
        title: 'Upload failed',
        description: error.message || 'Please try again.',
      });
    } finally {
      setUploading(false);
    }
  };

  const handleCopyUrl = async (url: string) => {
    const success = await copyToClipboard(url);
    if (success) {
      addToast({
        type: 'success',
        title: 'URL copied',
        description: 'Image URL copied to clipboard.',
      });
    } else {
      addToast({
        type: 'error',
        title: 'Copy failed',
        description: 'Failed to copy URL to clipboard.',
      });
    }
  };

  const handleDelete = async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      const response = await fetch(`/api/images/${imageId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setImages(images.filter(img => img.$id !== imageId));
        addToast({
          type: 'success',
          title: 'Image deleted',
          description: 'Image has been deleted successfully.',
        });
      } else {
        addToast({
          type: 'error',
          title: 'Delete failed',
          description: result.error || 'Failed to delete image.',
        });
      }
    } catch (error) {
      console.error('Delete failed:', error);
      addToast({
        type: 'error',
        title: 'Delete failed',
        description: 'Failed to delete image.',
      });
    }
  };

  const filteredImages = images.filter(image =>
    image.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    image.fileName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Images</h1>
            <p className="text-gray-400 mt-1">
              Upload, manage, and share your images
            </p>
          </div>
        </div>

        {/* Upload Section */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">Upload Images</h2>
          <FileUpload 
            onUpload={handleUpload}
            disabled={uploading}
            maxFiles={10}
            maxSize={10 * 1024 * 1024} // 10MB
          />
          {uploading && (
            <div className="mt-4 text-center">
              <div className="spinner mx-auto mb-2"></div>
              <p className="text-gray-400">Uploading images...</p>
            </div>
          )}
        </div>

        {/* Images Management */}
        <div className="card">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <h2 className="text-xl font-semibold text-white mb-4 sm:mb-0">
              Your Images ({filteredImages.length})
            </h2>
            
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search images..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              {/* View Mode Toggle */}
              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Images Display */}
          {loading ? (
            <div className="text-center py-12">
              <div className="spinner mx-auto mb-4"></div>
              <p className="text-gray-400">Loading images...</p>
            </div>
          ) : filteredImages.length === 0 ? (
            <div className="text-center py-12">
              <FileImage className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 text-lg mb-2">
                {searchTerm ? 'No images found' : 'No images uploaded yet'}
              </p>
              <p className="text-gray-500">
                {searchTerm ? 'Try adjusting your search terms' : 'Upload your first image to get started!'}
              </p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredImages.map((image) => (
                <ImageCard key={image.$id} image={image} onCopyUrl={handleCopyUrl} onDelete={handleDelete} />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredImages.map((image) => (
                <ImageListItem key={image.$id} image={image} onCopyUrl={handleCopyUrl} onDelete={handleDelete} />
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}

function ImageCard({ image, onCopyUrl, onDelete }: { 
  image: Image; 
  onCopyUrl: (url: string) => void;
  onDelete: (id: string) => void;
}) {
  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-750 transition-colors">
      <div className="aspect-video bg-gray-700 relative">
        <img
          src={image.url}
          alt={image.originalName}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
          <Button size="sm" onClick={() => window.open(image.url, '_blank')}>
            <ExternalLink className="w-4 h-4" />
          </Button>
          <Button size="sm" onClick={() => onCopyUrl(image.url)}>
            <Copy className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="destructive" onClick={() => onDelete(image.$id)}>
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="font-medium text-white truncate" title={image.originalName}>
          {image.originalName}
        </h3>
        <div className="flex items-center justify-between mt-2 text-sm text-gray-400">
          <span>{formatFileSize(image.fileSize)}</span>
          <div className="flex items-center space-x-1">
            <Eye className="w-3 h-3" />
            <span>{image.downloadCount}</span>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          {formatDate(image.$createdAt)}
        </p>
      </div>
    </div>
  );
}

function ImageListItem({ image, onCopyUrl, onDelete }: { 
  image: Image; 
  onCopyUrl: (url: string) => void;
  onDelete: (id: string) => void;
}) {
  return (
    <div className="flex items-center space-x-4 p-4 bg-gray-800 rounded-lg hover:bg-gray-750 transition-colors">
      <img
        src={image.url}
        alt={image.originalName}
        className="w-16 h-16 object-cover rounded-lg"
      />
      
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-white truncate">{image.originalName}</h3>
        <div className="flex items-center space-x-4 mt-1 text-sm text-gray-400">
          <span>{formatFileSize(image.fileSize)}</span>
          <div className="flex items-center space-x-1">
            <Eye className="w-3 h-3" />
            <span>{image.downloadCount}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar className="w-3 h-3" />
            <span>{formatDate(image.$createdAt)}</span>
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <Button size="sm" variant="ghost" onClick={() => window.open(image.url, '_blank')}>
          <ExternalLink className="w-4 h-4" />
        </Button>
        <Button size="sm" variant="ghost" onClick={() => onCopyUrl(image.url)}>
          <Copy className="w-4 h-4" />
        </Button>
        <Button size="sm" variant="destructive" onClick={() => onDelete(image.$id)}>
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
