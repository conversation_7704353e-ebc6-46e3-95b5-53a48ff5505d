'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toaster';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Package, 
  Check, 
  Star, 
  HardDrive,
  Zap,
  Shield,
  Crown
} from 'lucide-react';
import { StoragePlan } from '@/lib/types';
import { formatFileSize, formatCredits } from '@/lib/utils';

export default function StorePage() {
  const { user, refreshUser } = useAuth();
  const { addToast } = useToast();
  const [plans, setPlans] = useState<StoragePlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/storage-plans');
      const result = await response.json();

      if (result.success) {
        setPlans(result.data);
      } else {
        addToast({
          type: 'error',
          title: 'Failed to load plans',
          description: result.error || 'Please try again later.',
        });
      }
    } catch (error) {
      console.error('Failed to fetch plans:', error);
      addToast({
        type: 'error',
        title: 'Failed to load plans',
        description: 'Please try again later.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async (plan: StoragePlan) => {
    if (!user) return;

    if (user.credits < plan.price) {
      addToast({
        type: 'error',
        title: 'Insufficient Credits',
        description: `You need ${formatCredits(plan.price)} credits to purchase this plan. You have ${formatCredits(user.credits)}.`,
      });
      return;
    }

    setPurchasing(plan.$id);

    try {
      const response = await fetch('/api/storage-plans/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId: plan.$id }),
      });

      const result = await response.json();

      if (result.success) {
        addToast({
          type: 'success',
          title: 'Purchase Successful',
          description: `${plan.name} plan purchased successfully! Your storage has been upgraded.`,
        });
        await refreshUser();
      } else {
        addToast({
          type: 'error',
          title: 'Purchase Failed',
          description: result.error || 'Failed to complete purchase. Please try again.',
        });
      }
    } catch (error) {
      console.error('Purchase failed:', error);
      addToast({
        type: 'error',
        title: 'Purchase Failed',
        description: 'Failed to complete purchase. Please try again.',
      });
    } finally {
      setPurchasing(null);
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'starter':
        return Package;
      case 'pro':
        return Star;
      case 'business':
        return Crown;
      default:
        return Package;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'starter':
        return 'bg-blue-600';
      case 'pro':
        return 'bg-purple-600';
      case 'business':
        return 'bg-yellow-600';
      default:
        return 'bg-gray-600';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white">Storage Plans</h1>
          <p className="text-gray-400 mt-2">
            Upgrade your storage to upload more images
          </p>
          <div className="mt-4 inline-flex items-center space-x-2 px-4 py-2 bg-purple-600/20 text-purple-400 rounded-full">
            <HardDrive className="w-4 h-4" />
            <span>Current Credits: {formatCredits(user?.credits || 0)}</span>
          </div>
        </div>

        {/* Current Usage */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-4">Current Usage</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {formatFileSize(user?.storageUsed || 0)}
              </div>
              <div className="text-sm text-gray-400">Used</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {formatFileSize(user?.storageQuota || 0)}
              </div>
              <div className="text-sm text-gray-400">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {formatFileSize((user?.storageQuota || 0) - (user?.storageUsed || 0))}
              </div>
              <div className="text-sm text-gray-400">Available</div>
            </div>
          </div>
        </div>

        {/* Plans Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-400">Loading storage plans...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              const Icon = getPlanIcon(plan.name);
              const colorClass = getPlanColor(plan.name);
              const isPopular = plan.name.toLowerCase() === 'pro';
              
              return (
                <div
                  key={plan.$id}
                  className={`relative card hover-glow ${isPopular ? 'ring-2 ring-purple-500' : ''}`}
                >
                  {isPopular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Most Popular
                      </div>
                    </div>
                  )}
                  
                  <div className="text-center">
                    <div className={`w-16 h-16 ${colorClass} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                    
                    <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                    <p className="text-gray-400 mb-4">{plan.description}</p>
                    
                    <div className="mb-6">
                      <div className="text-3xl font-bold text-white">
                        {formatCredits(plan.price)}
                      </div>
                      <div className="text-sm text-gray-400">credits</div>
                    </div>
                    
                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Check className="w-4 h-4 text-green-400" />
                          <span className="text-sm text-gray-300">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <Button
                      onClick={() => handlePurchase(plan)}
                      loading={purchasing === plan.$id}
                      disabled={purchasing !== null}
                      className="w-full"
                      variant={isPopular ? 'primary' : 'outline'}
                    >
                      {purchasing === plan.$id ? 'Processing...' : 'Purchase Plan'}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Features Comparison */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-6">Why Upgrade?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Faster Uploads</h3>
              <p className="text-gray-400 text-sm">
                Higher storage limits mean you can upload more images without worrying about space.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Priority Support</h3>
              <p className="text-gray-400 text-sm">
                Get faster response times and dedicated support for your account.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Advanced Features</h3>
              <p className="text-gray-400 text-sm">
                Access to analytics, custom branding, and team management tools.
              </p>
            </div>
          </div>
        </div>

        {/* FAQ */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-6">Frequently Asked Questions</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-white mb-2">How do credits work?</h3>
              <p className="text-gray-400 text-sm">
                Credits are our virtual currency. You can purchase storage plans using credits. 
                New users start with 500 credits.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-white mb-2">Can I upgrade multiple times?</h3>
              <p className="text-gray-400 text-sm">
                Yes! Each purchase adds to your total storage quota. You can buy multiple plans 
                to increase your storage as needed.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-white mb-2">What happens to my images if I run out of storage?</h3>
              <p className="text-gray-400 text-sm">
                Your existing images remain safe and accessible. You'll just need to upgrade 
                your storage to upload new images.
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
