'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { account, databases, DATABASE_ID, COLLECTIONS } from '@/lib/appwrite';
import { User } from '@/lib/types';
import { Models } from 'appwrite';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithOAuth: (provider: 'discord' | 'google') => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshUser = async () => {
    try {
      const currentUser = await account.get();
      if (currentUser) {
        // Get user data from database
        try {
          const userDoc = await databases.getDocument(
            DATABASE_ID,
            COLLECTIONS.USERS,
            currentUser.$id
          );
          setUser(userDoc as User);
        } catch (error) {
          // If user document doesn't exist, create it
          const newUser: Partial<User> = {
            name: currentUser.name,
            email: currentUser.email,
            isAdmin: false,
            credits: 500.0,
            storageQuota: 50 * 1024 * 1024, // 50MB
            storageUsed: 0,
            provider: 'google', // Default, will be updated based on actual provider
            providerId: currentUser.$id,
            embedTitle: 'AveImgCloud',
            embedDescription: 'Image hosted on AveImgCloud',
            embedFooter: 'Powered by AveImgCloud',
          };

          const createdUser = await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.USERS,
            currentUser.$id,
            newUser
          );
          setUser(createdUser as User);
        }
      }
    } catch (error) {
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    await account.createEmailPasswordSession(email, password);
    await refreshUser();
  };

  const loginWithOAuth = async (provider: 'discord' | 'google') => {
    const redirectUrl = `${window.location.origin}/auth/callback`;
    await account.createOAuth2Session(provider, redirectUrl, redirectUrl);
  };

  const logout = async () => {
    await account.deleteSession('current');
    setUser(null);
  };

  useEffect(() => {
    refreshUser();
  }, []);

  const value = {
    user,
    loading,
    login,
    loginWithOAuth,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
