import { NextRequest } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS, Query } from './appwrite';

export async function authenticateA<PERSON><PERSON><PERSON>(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
  
  if (!apiKey || !apiKey.startsWith('aic_')) {
    return null;
  }

  try {
    // Find user by API key
    const users = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.USERS,
      [
        Query.equal('apiKey', apiKey),
        Query.limit(1)
      ]
    );

    if (users.documents.length === 0) {
      return null;
    }

    return users.documents[0];
  } catch (error) {
    console.error('API key authentication error:', error);
    return null;
  }
}
