import { Client, Account, Databases, Storage, Teams, Query } from 'appwrite';

// Appwrite configuration
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!);

// Initialize Appwrite services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const teams = new Teams(client);

// Database and collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!;
export const STORAGE_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID!;

// Collection IDs
export const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID || 'users',
  IMAGES: process.env.NEXT_PUBLIC_APPWRITE_IMAGES_COLLECTION_ID || 'images',
  STORAGE_PLANS: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_PLANS_COLLECTION_ID || 'storage_plans',
  PURCHASES: process.env.NEXT_PUBLIC_APPWRITE_PURCHASES_COLLECTION_ID || 'purchases',
};

// Export Query for convenience
export { Query };

// Helper function to get current user
export const getCurrentUser = async () => {
  try {
    return await account.get();
  } catch (error) {
    return null;
  }
};

// Helper function to check if user is admin
export const isUserAdmin = async (userId: string) => {
  try {
    const userDoc = await databases.getDocument(DATABASE_ID, COLLECTIONS.USERS, userId);
    return userDoc.isAdmin || false;
  } catch (error) {
    return false;
  }
};

export default client;
