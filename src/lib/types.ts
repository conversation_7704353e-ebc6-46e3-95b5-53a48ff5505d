// User types
export interface User {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  email: string;
  avatar?: string;
  isAdmin: boolean;
  credits: number;
  storageQuota: number; // in bytes
  storageUsed: number; // in bytes
  provider: 'discord' | 'google';
  providerId: string;
  apiKey?: string;
  embedTitle?: string;
  embedDescription?: string;
  embedFooter?: string;
}

// Image types
export interface Image {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  url: string;
  fileId: string; // Appwrite storage file ID
  thumbnailUrl?: string;
  isPublic: boolean;
  downloadCount: number;
  tags?: string[];
}

// Storage Plan types
export interface StoragePlan {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  description: string;
  price: number; // in credits
  storageAmount: number; // in bytes
  isActive: boolean;
  features: string[];
}

// Purchase types
export interface Purchase {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  planId: string;
  planName: string;
  price: number;
  storageAmount: number;
  status: 'pending' | 'completed' | 'failed';
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Upload types
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// Dashboard stats types
export interface DashboardStats {
  totalUsers: number;
  totalImages: number;
  totalStorage: number;
  totalRevenue: number;
  recentUploads: Image[];
  storageUsage: {
    used: number;
    total: number;
    percentage: number;
  };
}

// User stats types
export interface UserStats {
  totalImages: number;
  storageUsed: number;
  storageQuota: number;
  downloadCount: number;
  recentImages: Image[];
}

// ShareX configuration types
export interface ShareXConfig {
  Version: string;
  Name: string;
  DestinationType: string;
  RequestMethod: string;
  RequestURL: string;
  Headers: Record<string, string>;
  Body: string;
  URL: string;
  ThumbnailURL?: string;
  DeletionURL?: string;
}
