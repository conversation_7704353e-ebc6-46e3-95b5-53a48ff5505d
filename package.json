{"name": "aveimgcloud", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-database.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "appwrite": "^16.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.468.0", "next": "15.3.4", "node-appwrite": "^17.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-hook-form": "^7.53.2", "recharts": "^2.13.3", "tailwind-merge": "^2.5.4", "zod": "^3.23.8"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-config-next": "15.3.4", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.6.3"}}