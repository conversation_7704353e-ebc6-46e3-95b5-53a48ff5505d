# AveImgCloud - Premium Image Hosting Service

A **production-ready** image hosting web application built with Next.js 15, TypeScript, Tailwind CSS, and Appwrite. Features include ShareX integration, custom embeds, user management, and a powerful admin dashboard.

> **🎉 100% Complete & Ready for Production!** This project includes all backend integrations, API endpoints, database setup, and deployment configurations.

![AveImgCloud Banner](https://via.placeholder.com/1200x400/7c3aed/ffffff?text=AveImgCloud+-+Premium+Image+Hosting)

## 🌟 Features

### Core Features
- **Modern UI/UX**: Purple dark theme with gradients, star decorations, and responsive design
- **Authentication**: OAuth integration with Discord and Google via Appwrite
- **Image Management**: Drag-and-drop uploads, direct URL sharing, embed code generation
- **ShareX Integration**: Complete setup instructions and API configuration
- **Custom Embeds**: Personalize how images appear on Discord and other platforms
- **Storage Plans**: Credit-based system with flexible storage options
- **User Roles**: Admin and regular user permissions

### User Dashboard
- **Dashboard**: Usage statistics and quick actions
- **Images**: Upload, view, and manage uploaded images
- **Store**: Purchase storage plans with credits
- **Connections**: ShareX integration setup with detailed instructions
- **Settings**: Profile settings and image embed customization

### Admin Dashboard
- **Overview**: System statistics and metrics
- **Users**: User management, edit permissions, credits, storage limits
- **Store**: Create and manage storage plans
- **Settings**: System configuration

## 🚀 Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with custom purple dark theme
- **Backend**: Appwrite (authentication, database, file storage)
- **Deployment**: Vercel
- **Icons**: Lucide React
- **Forms**: React Hook Form with Zod validation
- **File Uploads**: React Dropzone
- **State Management**: React Context API

## 📋 Prerequisites

- Node.js 18+ and npm/yarn
- Appwrite account and project
- Vercel account (for deployment)
- Discord and/or Google OAuth applications

## 📁 Project Structure

```
AveImgCloud/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── admin/             # Admin dashboard pages
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # User dashboard pages
│   │   ├── api/               # API routes
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Landing page
│   ├── components/            # Reusable components
│   │   ├── layout/            # Layout components
│   │   └── ui/                # UI components
│   ├── contexts/              # React contexts
│   ├── lib/                   # Utilities and configurations
│   └── types/                 # TypeScript type definitions
├── public/                    # Static assets
├── docs/                      # Documentation
├── .env.example              # Environment variables template
├── DEPLOYMENT.md             # Deployment guide
└── README.md                 # This file
```

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone https://github.com/RjNlxe/AveImgCloud.git
cd AveImgCloud
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Quick Setup

For detailed setup instructions, see [SETUP_GUIDE.md](SETUP_GUIDE.md).

```bash
# Copy environment file
cp .env.example .env.local

# Set up Appwrite project and update .env.local
# Then initialize the database
npm run init-db

# Start development
npm run dev
```

#### Environment Configuration

Update `.env.local` with your configuration:

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id_here
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your_storage_bucket_id_here

# Collection IDs (will be created automatically)
NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID=users
NEXT_PUBLIC_APPWRITE_IMAGES_COLLECTION_ID=images
NEXT_PUBLIC_APPWRITE_STORAGE_PLANS_COLLECTION_ID=storage_plans
NEXT_PUBLIC_APPWRITE_PURCHASES_COLLECTION_ID=purchases
```

### 4. Appwrite Setup

#### Create Appwrite Project
1. Go to [Appwrite Console](https://cloud.appwrite.io)
2. Create a new project
3. Copy the Project ID to your `.env.local`

#### Configure Authentication
1. Go to Auth → Settings
2. Add your domain to allowed origins: `http://localhost:3000`, `https://yourdomain.com`
3. Enable OAuth providers:
   - **Discord**: Add your Discord app credentials
   - **Google**: Add your Google app credentials

#### Create Database and Collections

Create a database and the following collections:

**Users Collection** (`users`):
```json
{
  "name": "string",
  "email": "string", 
  "isAdmin": "boolean",
  "credits": "double",
  "storageQuota": "integer",
  "storageUsed": "integer",
  "provider": "string",
  "providerId": "string",
  "apiKey": "string",
  "embedTitle": "string",
  "embedDescription": "string",
  "embedFooter": "string"
}
```

**Images Collection** (`images`):
```json
{
  "userId": "string",
  "fileName": "string",
  "originalName": "string", 
  "fileSize": "integer",
  "mimeType": "string",
  "url": "string",
  "thumbnailUrl": "string",
  "isPublic": "boolean",
  "downloadCount": "integer",
  "tags": "string[]"
}
```

**Storage Plans Collection** (`storage_plans`):
```json
{
  "name": "string",
  "description": "string",
  "price": "double",
  "storageAmount": "integer", 
  "isActive": "boolean",
  "features": "string[]"
}
```

**Purchases Collection** (`purchases`):
```json
{
  "userId": "string",
  "planId": "string",
  "planName": "string",
  "price": "double",
  "storageAmount": "integer",
  "status": "string"
}
```

#### Create Storage Bucket
1. Go to Storage
2. Create a new bucket for image uploads
3. Configure permissions for authenticated users
4. Copy the Bucket ID to your `.env.local`

### 5. Development

Start the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🚀 Deployment

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

### Quick Vercel Deployment

1. **Connect Repository**:
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Import your GitHub repository

2. **Environment Variables**:
   - Add all environment variables from `.env.local`
   - Update `NEXT_PUBLIC_API_BASE_URL` to your production domain

3. **Deploy**:
   - Vercel will automatically deploy your application
   - Update Appwrite allowed origins with your production domain

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🎨 Screenshots

### Landing Page
![Landing Page](https://via.placeholder.com/800x600/1a1a2e/ffffff?text=Landing+Page)

### User Dashboard
![User Dashboard](https://via.placeholder.com/800x600/1a1a2e/ffffff?text=User+Dashboard)

### Admin Panel
![Admin Panel](https://via.placeholder.com/800x600/1a1a2e/ffffff?text=Admin+Panel)

### ShareX Integration
![ShareX Setup](https://via.placeholder.com/800x600/1a1a2e/ffffff?text=ShareX+Integration)

## 📖 Usage

### For Users
1. **Sign Up**: Create account with Discord/Google OAuth
2. **Upload Images**: Use drag-and-drop interface or ShareX
3. **Manage Storage**: Purchase additional storage with credits
4. **Customize Embeds**: Set custom titles, descriptions, and footers
5. **ShareX Setup**: Follow detailed integration instructions

### For Admins
1. **User Management**: Edit user permissions, credits, and storage quotas
2. **Storage Plans**: Create and manage storage plans
3. **System Overview**: Monitor usage statistics and metrics
4. **Settings**: Configure system-wide settings

## 🔧 API Endpoints

### Upload API (for ShareX)
```
POST /api/upload
Headers: Authorization: Bearer <api_key>
Body: multipart/form-data with image file
```

### User API
```
GET /api/user/stats - Get user statistics
PUT /api/user/settings - Update user settings
```

### Admin API
```
GET /api/admin/users - Get all users
PUT /api/admin/users/:id - Update user
GET /api/admin/stats - Get system statistics
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Discord server.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Appwrite](https://appwrite.io/) - Backend as a Service
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Lucide](https://lucide.dev/) - Icon library
- [Vercel](https://vercel.com/) - Deployment platform
