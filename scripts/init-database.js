/**
 * Database Initialization Script for AveImgCloud
 * 
 * This script creates the necessary collections and default data in Appwrite.
 * Run this after setting up your Appwrite project and configuring environment variables.
 * 
 * Usage: node scripts/init-database.js
 */

const sdk = require('node-appwrite');
require('dotenv').config({ path: '.env.local' });

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY); // You need to set this in your .env.local

const databases = new sdk.Databases(client);
const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

// Collection schemas
const collections = {
  users: {
    name: 'users',
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'isAdmin', type: 'boolean', required: false, default: false },
      { key: 'credits', type: 'double', required: false, default: 500.0 },
      { key: 'storageQuota', type: 'integer', required: false, default: 52428800 }, // 50MB
      { key: 'storageUsed', type: 'integer', required: false, default: 0 },
      { key: 'provider', type: 'string', size: 50, required: true },
      { key: 'providerId', type: 'string', size: 255, required: true },
      { key: 'apiKey', type: 'string', size: 255, required: false },
      { key: 'embedTitle', type: 'string', size: 255, required: false },
      { key: 'embedDescription', type: 'string', size: 4096, required: false },
      { key: 'embedFooter', type: 'string', size: 2048, required: false }
    ],
    indexes: [
      { key: 'email_index', type: 'unique', attributes: ['email'] },
      { key: 'apiKey_index', type: 'unique', attributes: ['apiKey'] }
    ]
  },
  images: {
    name: 'images',
    attributes: [
      { key: 'userId', type: 'string', size: 255, required: true },
      { key: 'fileName', type: 'string', size: 255, required: true },
      { key: 'originalName', type: 'string', size: 255, required: true },
      { key: 'fileSize', type: 'integer', required: true },
      { key: 'mimeType', type: 'string', size: 100, required: true },
      { key: 'url', type: 'string', size: 2048, required: true },
      { key: 'fileId', type: 'string', size: 255, required: true },
      { key: 'thumbnailUrl', type: 'string', size: 2048, required: false },
      { key: 'isPublic', type: 'boolean', required: false, default: true },
      { key: 'downloadCount', type: 'integer', required: false, default: 0 },
      { key: 'tags', type: 'string', size: 1000, array: true, required: false }
    ],
    indexes: [
      { key: 'userId_index', type: 'key', attributes: ['userId'] },
      { key: 'fileName_index', type: 'fulltext', attributes: ['fileName', 'originalName'] }
    ]
  },
  storage_plans: {
    name: 'storage_plans',
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'description', type: 'string', size: 1000, required: true },
      { key: 'price', type: 'double', required: true },
      { key: 'storageAmount', type: 'integer', required: true },
      { key: 'isActive', type: 'boolean', required: false, default: true },
      { key: 'features', type: 'string', size: 255, array: true, required: false }
    ],
    indexes: [
      { key: 'price_index', type: 'key', attributes: ['price'] },
      { key: 'active_index', type: 'key', attributes: ['isActive'] }
    ]
  },
  purchases: {
    name: 'purchases',
    attributes: [
      { key: 'userId', type: 'string', size: 255, required: true },
      { key: 'planId', type: 'string', size: 255, required: true },
      { key: 'planName', type: 'string', size: 255, required: true },
      { key: 'price', type: 'double', required: true },
      { key: 'storageAmount', type: 'integer', required: true },
      { key: 'status', type: 'string', size: 50, required: false, default: 'completed' }
    ],
    indexes: [
      { key: 'userId_index', type: 'key', attributes: ['userId'] },
      { key: 'status_index', type: 'key', attributes: ['status'] }
    ]
  }
};

// Default storage plans
const defaultPlans = [
  {
    name: 'Starter',
    description: 'Perfect for personal use',
    price: 50.0,
    storageAmount: 104857600, // 100MB
    isActive: true,
    features: ['100MB Storage', 'Unlimited uploads', 'ShareX integration', 'Basic support']
  },
  {
    name: 'Pro',
    description: 'Great for content creators',
    price: 150.0,
    storageAmount: 524288000, // 500MB
    isActive: true,
    features: ['500MB Storage', 'Unlimited uploads', 'ShareX integration', 'Custom embeds', 'Priority support', 'Advanced analytics']
  },
  {
    name: 'Business',
    description: 'For teams and businesses',
    price: 300.0,
    storageAmount: 2147483648, // 2GB
    isActive: true,
    features: ['2GB Storage', 'Unlimited uploads', 'ShareX integration', 'Custom embeds', 'Priority support', 'Advanced analytics', 'Team management', 'Custom branding']
  }
];

async function createCollection(collectionId, schema) {
  try {
    console.log(`Creating collection: ${schema.name}`);
    
    // Create collection
    await databases.createCollection(DATABASE_ID, collectionId, schema.name);
    
    // Create attributes
    for (const attr of schema.attributes) {
      console.log(`  Adding attribute: ${attr.key}`);
      
      if (attr.type === 'string') {
        await databases.createStringAttribute(
          DATABASE_ID,
          collectionId,
          attr.key,
          attr.size,
          attr.required,
          attr.default,
          attr.array || false
        );
      } else if (attr.type === 'integer') {
        await databases.createIntegerAttribute(
          DATABASE_ID,
          collectionId,
          attr.key,
          attr.required,
          attr.min,
          attr.max,
          attr.default,
          attr.array || false
        );
      } else if (attr.type === 'double') {
        await databases.createFloatAttribute(
          DATABASE_ID,
          collectionId,
          attr.key,
          attr.required,
          attr.min,
          attr.max,
          attr.default,
          attr.array || false
        );
      } else if (attr.type === 'boolean') {
        await databases.createBooleanAttribute(
          DATABASE_ID,
          collectionId,
          attr.key,
          attr.required,
          attr.default,
          attr.array || false
        );
      }
      
      // Wait a bit between attribute creation
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Create indexes
    if (schema.indexes) {
      for (const index of schema.indexes) {
        console.log(`  Adding index: ${index.key}`);
        await databases.createIndex(
          DATABASE_ID,
          collectionId,
          index.key,
          index.type,
          index.attributes,
          index.orders
        );
        
        // Wait a bit between index creation
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`✅ Collection ${schema.name} created successfully`);
  } catch (error) {
    console.error(`❌ Error creating collection ${schema.name}:`, error.message);
  }
}

async function createDefaultPlans() {
  try {
    console.log('Creating default storage plans...');
    
    for (const plan of defaultPlans) {
      await databases.createDocument(
        DATABASE_ID,
        'storage_plans',
        sdk.ID.unique(),
        plan
      );
      console.log(`  Created plan: ${plan.name}`);
    }
    
    console.log('✅ Default storage plans created successfully');
  } catch (error) {
    console.error('❌ Error creating default plans:', error.message);
  }
}

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing AveImgCloud database...\n');
    
    // Create collections
    for (const [collectionId, schema] of Object.entries(collections)) {
      await createCollection(collectionId, schema);
      console.log(''); // Empty line for readability
    }
    
    // Wait a bit before creating default data
    console.log('Waiting for collections to be ready...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Create default storage plans
    await createDefaultPlans();
    
    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Set up OAuth providers in Appwrite Console');
    console.log('2. Configure storage bucket permissions');
    console.log('3. Create your first admin user by registering and updating the isAdmin field');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    process.exit(1);
  }
}

// Run the initialization
initializeDatabase();
