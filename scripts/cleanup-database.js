/**
 * Database Cleanup Script for AveImgCloud
 * 
 * This script deletes existing collections to allow for recreation with correct schema.
 * Run this before running init-database.js if you need to recreate collections.
 * 
 * Usage: node scripts/cleanup-database.js
 */

const sdk = require('node-appwrite');
require('dotenv').config({ path: '.env.local' });

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);
const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

// Collections to delete
const collectionsToDelete = ['users', 'images', 'storage_plans', 'purchases'];

async function deleteCollection(collectionId) {
  try {
    console.log(`Deleting collection: ${collectionId}`);
    await databases.deleteCollection(DATABASE_ID, collectionId);
    console.log(`✅ Collection ${collectionId} deleted successfully`);
  } catch (error) {
    if (error.code === 404) {
      console.log(`⚠️  Collection ${collectionId} not found (already deleted or never existed)`);
    } else {
      console.error(`❌ Error deleting collection ${collectionId}:`, error.message);
    }
  }
}

async function cleanupDatabase() {
  try {
    console.log('🧹 Cleaning up AveImgCloud database...\n');
    
    // Delete collections
    for (const collectionId of collectionsToDelete) {
      await deleteCollection(collectionId);
      console.log(''); // Empty line for readability
    }
    
    console.log('🎉 Database cleanup completed successfully!');
    console.log('\nNext step: Run "pnpm run init-db" to recreate collections with correct schema');
    
  } catch (error) {
    console.error('❌ Database cleanup failed:', error.message);
    process.exit(1);
  }
}

// Run the cleanup
cleanupDatabase();
