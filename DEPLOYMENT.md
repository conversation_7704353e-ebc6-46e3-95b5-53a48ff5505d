# AveImgCloud Deployment Guide

This guide provides step-by-step instructions for deploying AveImgCloud to production.

## Prerequisites

- Appwrite Cloud account or self-hosted Appwrite instance
- Vercel account (for deployment)
- Discord Developer Application (for OAuth)
- Google Cloud Console project (for OAuth)
- Domain name (optional, but recommended)

## 1. Appwrite Setup

### Create Appwrite Project

1. Go to [Appwrite Console](https://cloud.appwrite.io)
2. Create a new project
3. Note down your Project ID

### Configure Authentication

1. Navigate to **Auth → Settings**
2. Add your domains to **Allowed Origins**:
   - Development: `http://localhost:3000`
   - Production: `https://yourdomain.com`

### Setup OAuth Providers

#### Discord OAuth
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to **OAuth2** section
4. Add redirect URI: `https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/discord/[PROJECT_ID]`
5. Copy Client ID and Client Secret
6. In Appwrite Console:
   - Go to **Auth → Settings → OAuth2 Providers**
   - Enable Discord
   - Enter Client ID and Client Secret

#### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing
3. Enable Google+ API
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**
5. Add redirect URI: `https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/google/[PROJECT_ID]`
6. Copy Client ID and Client Secret
7. In Appwrite Console:
   - Go to **Auth → Settings → OAuth2 Providers**
   - Enable Google
   - Enter Client ID and Client Secret

### Create Database and Collections

1. Go to **Databases** → **Create Database**
2. Note down the Database ID

Create the following collections with these attributes:

#### Users Collection (`users`)
```json
{
  "name": { "type": "string", "size": 255, "required": true },
  "email": { "type": "string", "size": 255, "required": true },
  "isAdmin": { "type": "boolean", "required": true, "default": false },
  "credits": { "type": "double", "required": true, "default": 500.0 },
  "storageQuota": { "type": "integer", "required": true, "default": ******** },
  "storageUsed": { "type": "integer", "required": true, "default": 0 },
  "provider": { "type": "string", "size": 50, "required": true },
  "providerId": { "type": "string", "size": 255, "required": true },
  "apiKey": { "type": "string", "size": 255, "required": false },
  "embedTitle": { "type": "string", "size": 255, "required": false },
  "embedDescription": { "type": "string", "size": 4096, "required": false },
  "embedFooter": { "type": "string", "size": 2048, "required": false }
}
```

**Permissions:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `users`

#### Images Collection (`images`)
```json
{
  "userId": { "type": "string", "size": 255, "required": true },
  "fileName": { "type": "string", "size": 255, "required": true },
  "originalName": { "type": "string", "size": 255, "required": true },
  "fileSize": { "type": "integer", "required": true },
  "mimeType": { "type": "string", "size": 100, "required": true },
  "url": { "type": "string", "size": 2048, "required": true },
  "thumbnailUrl": { "type": "string", "size": 2048, "required": false },
  "isPublic": { "type": "boolean", "required": true, "default": true },
  "downloadCount": { "type": "integer", "required": true, "default": 0 },
  "tags": { "type": "string", "size": 1000, "array": true, "required": false }
}
```

**Permissions:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `users`

#### Storage Plans Collection (`storage_plans`)
```json
{
  "name": { "type": "string", "size": 255, "required": true },
  "description": { "type": "string", "size": 1000, "required": true },
  "price": { "type": "double", "required": true },
  "storageAmount": { "type": "integer", "required": true },
  "isActive": { "type": "boolean", "required": true, "default": true },
  "features": { "type": "string", "size": 255, "array": true, "required": false }
}
```

**Permissions:**
- Read: `users`
- Create: `admins` (create a team for admins)
- Update: `admins`
- Delete: `admins`

#### Purchases Collection (`purchases`)
```json
{
  "userId": { "type": "string", "size": 255, "required": true },
  "planId": { "type": "string", "size": 255, "required": true },
  "planName": { "type": "string", "size": 255, "required": true },
  "price": { "type": "double", "required": true },
  "storageAmount": { "type": "integer", "required": true },
  "status": { "type": "string", "size": 50, "required": true, "default": "completed" }
}
```

**Permissions:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `users`

### Create Storage Bucket

1. Go to **Storage** → **Create Bucket**
2. Configure bucket settings:
   - **File Security**: Enabled
   - **Maximum File Size**: 10MB (10485760 bytes)
   - **Allowed File Extensions**: jpg, jpeg, png, gif, webp
3. Set permissions:
   - Read: `users`
   - Create: `users`
   - Update: `users`
   - Delete: `users`
4. Note down the Bucket ID

## 2. Environment Configuration

Create a `.env.local` file with your configuration:

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id_here
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your_storage_bucket_id_here

# Collection IDs
NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID=users
NEXT_PUBLIC_APPWRITE_IMAGES_COLLECTION_ID=images
NEXT_PUBLIC_APPWRITE_STORAGE_PLANS_COLLECTION_ID=storage_plans
NEXT_PUBLIC_APPWRITE_PURCHASES_COLLECTION_ID=purchases

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://yourdomain.com
```

## 3. Vercel Deployment

### Method 1: GitHub Integration (Recommended)

1. Push your code to GitHub
2. Go to [Vercel Dashboard](https://vercel.com/dashboard)
3. Click **New Project**
4. Import your GitHub repository
5. Configure build settings:
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
6. Add environment variables from your `.env.local`
7. Deploy

### Method 2: Vercel CLI

1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy:
   ```bash
   vercel --prod
   ```

4. Add environment variables:
   ```bash
   vercel env add NEXT_PUBLIC_APPWRITE_ENDPOINT
   vercel env add NEXT_PUBLIC_APPWRITE_PROJECT_ID
   # ... add all other environment variables
   ```

## 4. Post-Deployment Configuration

### Update Appwrite Settings

1. Add your production domain to Appwrite **Allowed Origins**
2. Update OAuth redirect URIs with your production domain
3. Test authentication flows

### Create Admin User

1. Register a new account on your deployed application
2. In Appwrite Console, go to **Databases** → **users** collection
3. Find your user document and update `isAdmin` to `true`

### Create Default Storage Plans

Add some default storage plans to the `storage_plans` collection:

```json
[
  {
    "name": "Starter",
    "description": "Perfect for personal use",
    "price": 50.0,
    "storageAmount": *********,
    "isActive": true,
    "features": ["100MB Storage", "Unlimited uploads", "ShareX integration", "Basic support"]
  },
  {
    "name": "Pro",
    "description": "Great for content creators",
    "price": 150.0,
    "storageAmount": ********0,
    "isActive": true,
    "features": ["500MB Storage", "Unlimited uploads", "ShareX integration", "Custom embeds", "Priority support"]
  },
  {
    "name": "Business",
    "description": "For teams and businesses",
    "price": 300.0,
    "storageAmount": **********,
    "isActive": true,
    "features": ["2GB Storage", "Unlimited uploads", "ShareX integration", "Custom embeds", "Priority support", "Team management"]
  }
]
```

## 5. Domain Configuration (Optional)

### Custom Domain on Vercel

1. Go to your project settings in Vercel
2. Navigate to **Domains**
3. Add your custom domain
4. Configure DNS records as instructed
5. Update environment variables with new domain

### SSL Certificate

Vercel automatically provides SSL certificates for all domains.

## 6. Monitoring and Maintenance

### Performance Monitoring

- Use Vercel Analytics for performance insights
- Monitor Appwrite usage in the console
- Set up error tracking (Sentry, LogRocket, etc.)

### Backup Strategy

- Appwrite Cloud provides automatic backups
- Consider exporting important data regularly
- Document your configuration for disaster recovery

### Updates

- Keep dependencies updated
- Monitor Appwrite and Vercel for updates
- Test updates in staging environment first

## 7. Troubleshooting

### Common Issues

1. **OAuth not working**: Check redirect URIs and allowed origins
2. **File uploads failing**: Verify storage bucket permissions and file size limits
3. **Database errors**: Check collection permissions and attribute types
4. **CORS errors**: Ensure all domains are added to Appwrite allowed origins

### Debug Mode

Enable debug mode by adding to your environment:
```env
NODE_ENV=development
```

### Logs

- Check Vercel function logs for API errors
- Monitor Appwrite logs for backend issues
- Use browser developer tools for client-side debugging

## 8. Security Considerations

- Regularly rotate API keys
- Monitor for unusual activity
- Keep dependencies updated
- Use environment variables for all secrets
- Enable rate limiting in Appwrite
- Configure proper CORS settings

## Support

For deployment issues:
- Check the [Appwrite Documentation](https://appwrite.io/docs)
- Visit [Vercel Documentation](https://vercel.com/docs)
- Open an issue on the GitHub repository
